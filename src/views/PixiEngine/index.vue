<script setup lang="ts">
import { onMounted, onBeforeUnmount, useTemplateRef } from 'vue';
import { CreationEngine, setEngine, destroyEngine } from './engine';
import { MainScreen } from './MainScreen';

const root = useTemplateRef('rootRef');

let app : CreationEngine | null = null;

onMounted(async () => {
  if (!root.value) {
    throw new Error('Root element not found');
  }

  app = new CreationEngine();

  await app.init({
    root: root.value,
    resizeTo: root.value,
    autoDensity: true,
    backgroundColor: 0x716c69,
  });

  setEngine(app);

  app.stage.addChild(new MainScreen());
});

onBeforeUnmount(() => {
  destroyEngine();
});
</script>

<template>
  <div ref="rootRef" class="w-full h-full"></div>
</template>

<style lang="scss" scoped>

</style>