import type { Ticker } from "pixi.js";
import { Assets, Container, Graphics, Sprite, Texture, TilingSprite } from "pixi.js";
import { getEngine } from "./engine";

/** The screen that holds the app */
export class MainScreen extends Container {
  /** Assets bundles required by this screen */
  public static assetBundles = ["main"];

  public mainContainer: Container;
  private paused = false;

  constructor() { 
    super(); 
    this.mainContainer = new Container();
    this.addChild(this.mainContainer);
    this.load();
  }

  private async load() {
    const engine = getEngine();

    let x = 0;

    // 创建马路开始部分
    const roadStart = await Assets.load('road-start.png');
    const roadStartSprite = new Sprite(roadStart);
    const roadRatio = roadStart.width / roadStart.height;
    roadStartSprite.x = x;
    roadStartSprite.width = engine.screen.height * roadRatio;
    roadStartSprite.height = engine.screen.height;
    x += roadStartSprite.width;
    this.mainContainer.addChild(roadStartSprite);


    // 创建车道
    for (let i = 0; i < 5; i++) {
      const lane = await this.createLane();
      lane.x = x;
      lane.
      x += lane.width;
      this.mainContainer.addChild(lane);

      if (i < 4) {
        const laneLine = await this.createLaneLine();
        laneLine.x = x;
        x += laneLine.width;
        this.mainContainer.addChild(laneLine);
      }
    }

    // 创建马路结束部分
    const roadEnd = await Assets.load('road-end.png');
    const roadEndSprite = new Sprite(roadEnd);
    const roadEndRatio = roadEnd.width / roadEnd.height;
    roadEndSprite.x = x;
    roadEndSprite.width = engine.screen.height * roadEndRatio;
    roadEndSprite.height = engine.screen.height;
    x += roadEndSprite.width;
    this.mainContainer.addChild(roadEndSprite);
  }

  // 车道
  private async createLane() {
    const engine = getEngine();
    const laneSize = 200;
    const laneContainer = new Container();

    // 马路
    const lane = new Graphics();
    lane.rect(0, 0, laneSize, engine.screen.height).fill(0x716c69);
    laneContainer.addChild(lane);


    // 井盖
    const hatchScale = 0.7;
    const hatch = await Assets.load('hatch_1.png');
    const hatchSprite = new Sprite(hatch);
    hatchSprite.x = (laneSize - laneSize * hatchScale) * 0.5;
    hatchSprite.y = engine.screen.height - laneSize * 1.6;
    hatchSprite.width = laneSize * hatchScale;
    hatchSprite.height = laneSize * hatchScale;
    laneContainer.addChild(hatchSprite);

    // 把车道缓存为纹理并返回Sprite
    laneContainer.cacheAsTexture(true);
    return new Sprite(laneContainer);
  }

  // 车道线
  private async createLaneLine() {
    const engine = getEngine();
    const laneLineCount = 10;
    const laneLineWidth = 8;
    const laneLine = new Graphics();
    const laneLineHeight = engine.screen.height / laneLineCount;
    laneLine.rect(0, 0, laneLineWidth, laneLineHeight).fill(0x716c69);
    laneLine.rect(0, laneLineHeight * 0.25, laneLineWidth, laneLineHeight * 0.5).fill(0xd3cfc9);
    const laneLineTexture = engine.renderer.generateTexture(laneLine);
    const laneLineSprite = new TilingSprite(laneLineTexture);
    laneLineSprite.height = engine.screen.height + laneLineHeight;
    laneLineSprite.y = -laneLineHeight * 0.5;
    return laneLineSprite;
  }

  /** Prepare the screen just before showing */
  public prepare() {}

  /** Update the screen */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public update(_time: Ticker) {}

  /** Pause gameplay - automatically fired when a popup is presented */
  public async pause() {
    this.mainContainer.interactiveChildren = false;
    this.paused = true;
  }

  /** Resume gameplay */
  public async resume() {
    this.mainContainer.interactiveChildren = true;
    this.paused = false;
  }

  /** Fully reset */
  public reset() {}

  /** Resize the screen, fired whenever window size changes */
  public resize(width: number, height: number) {
    const centerX = width * 0.5;
    const centerY = height * 0.5;

    this.mainContainer.x = centerX;
    this.mainContainer.y = centerY;
  }

  /** Show screen with animations */
  public async show(): Promise<void> {}

  /** Hide screen with animations */
  public async hide() {}

  /** Auto pause the app when window go out of focus */
  public blur() {}
}
